<template>
  <div class="login-container w-full max-w-md mx-auto">
    <!-- 主登录卡片 -->
    <div class="login-card backdrop-blur-xl bg-white/80 dark:bg-gray-900/80 rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/30 p-8 transform transition-all duration-500 hover:scale-105">
      <!-- Logo和标题区域 -->
      <div class="text-center mb-8">
        <!-- 动态Logo -->
        <div class="logo-container mx-auto mb-6 relative">
          <div class="logo-bg absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur-lg opacity-75 animate-pulse"></div>
          <div class="logo-main relative bg-gradient-to-r from-blue-500 to-purple-600 w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
        </div>

        <!-- 标题 -->
        <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent mb-2">
          欢迎回来
        </h1>
        <p class="text-gray-600 dark:text-gray-400 text-sm">
          登录到您的 AI Portal 管理后台
        </p>
      </div>
      
      <!-- 登录表单 -->
      <form class="space-y-6" @submit.prevent="handleLogin">
        <!-- 错误提示 -->
        <div v-if="errorMessage" class="error-message bg-red-50/80 dark:bg-red-900/20 backdrop-blur-sm border border-red-200/50 dark:border-red-800/30 text-red-700 dark:text-red-400 px-4 py-3 rounded-xl text-sm animate-shake">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            {{ errorMessage }}
          </div>
        </div>

        <div class="space-y-5">
          <!-- 用户名输入 -->
          <div class="input-group">
            <label for="username" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
              用户名
            </label>
            <div class="relative">
              <div class="input-icon absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <input
                id="username"
                name="username"
                type="text"
                required
                v-model="loginForm.username"
                class="input-field w-full pl-12 pr-4 py-4 bg-gray-50/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-300"
                placeholder="请输入用户名"
              />
            </div>
          </div>

          <!-- 密码输入 -->
          <div class="input-group">
            <label for="password" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
              密码
            </label>
            <div class="relative">
              <div class="input-icon absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <input
                id="password"
                name="password"
                type="password"
                required
                v-model="loginForm.password"
                class="input-field w-full pl-12 pr-4 py-4 bg-gray-50/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-300"
                placeholder="请输入密码"
              />
            </div>
          </div>

          <!-- 记住我和忘记密码 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                v-model="loginForm.rememberMe"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500/50 border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
              />
              <label for="remember-me" class="ml-3 block text-sm text-gray-700 dark:text-gray-300 font-medium">
                记住我
              </label>
            </div>
            <div class="text-sm">
              <a href="#" class="font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 transition-colors duration-200">
                忘记密码？
              </a>
            </div>
          </div>
        </div>

        <!-- 登录按钮 -->
        <div class="pt-2">
          <button
            type="submit"
            :disabled="isLoading"
            class="login-button group relative w-full flex justify-center py-4 px-4 border border-transparent text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <span class="absolute left-0 inset-y-0 flex items-center pl-4">
              <svg v-if="!isLoading" class="h-5 w-5 text-white/80 group-hover:text-white transition-colors duration-200" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
              </svg>
              <svg v-else class="animate-spin h-5 w-5 text-white/80" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            {{ isLoading ? '登录中...' : '立即登录' }}
          </button>
        </div>

        <!-- 分割线 -->
        <div class="relative my-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300/50 dark:border-gray-600/50"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-4 bg-white/80 dark:bg-gray-900/80 text-gray-500 dark:text-gray-400 font-medium">或者使用</span>
          </div>
        </div>

        <!-- OAuth2登录按钮 -->
        <OAuth2LoginButtons />


      </form>
    </div>

    <!-- 底部装饰 -->
    <div class="text-center mt-8 text-sm text-gray-500 dark:text-gray-400">
      <p>© 2024 AI Portal. 安全可靠的管理平台</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import OAuth2LoginButtons from '@/components/OAuth2LoginButtons.vue'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 状态
const isLoading = ref(false)
const errorMessage = ref('')

// 登录处理
const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    errorMessage.value = '请输入用户名和密码'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const result = await userStore.login({
      username: loginForm.username,
      password: loginForm.password
    })

    if (result.success) {
      // 登录成功，跳转到首页
      router.push('/')
    } else {
      errorMessage.value = result.message
    }
  } catch (error) {
    errorMessage.value = '登录失败，请稍后重试'
    console.error('登录错误:', error)
  } finally {
    isLoading.value = false
  }
}

// 页面加载动画
onMounted(() => {
  // 添加页面加载动画
  const loginCard = document.querySelector('.login-card')
  if (loginCard) {
    loginCard.style.opacity = '0'
    loginCard.style.transform = 'translateY(20px)'

    setTimeout(() => {
      loginCard.style.transition = 'all 0.6s ease-out'
      loginCard.style.opacity = '1'
      loginCard.style.transform = 'translateY(0)'
    }, 100)
  }
})
</script>

<style scoped>
/* 登录容器样式 */
.login-container {
  animation: fadeInUp 0.8s ease-out;
}

/* 登录卡片样式 */
.login-card {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.login-card:hover {
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.35),
    0 0 0 1px rgba(255, 255, 255, 0.15);
}

/* Logo动画 */
.logo-container {
  animation: float 3s ease-in-out infinite;
}

.logo-bg {
  animation: pulse 2s ease-in-out infinite;
}

/* 输入框样式 */
.input-field {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.input-field:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.15);
}

.input-group:hover .input-field {
  border-color: rgba(59, 130, 246, 0.3);
}

/* 登录按钮样式 */
.login-button {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.login-button:hover {
  background-position: right center;
  box-shadow:
    0 20px 40px -12px rgba(59, 130, 246, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 错误消息动画 */
.error-message {
  animation: shake 0.5s ease-in-out;
}

/* 动画定义 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .login-card {
    margin: 1rem;
    padding: 1.5rem;
  }

  .logo-main {
    width: 3rem;
    height: 3rem;
  }

  .login-button {
    padding: 0.875rem 1rem;
  }
}

/* 深色模式优化 */
.dark .login-card {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.dark .login-card:hover {
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.dark .input-field:focus {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.25);
}
</style>
